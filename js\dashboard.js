/**
 * Dashboard JS - Funciones para la página de dashboard administrativo
 */

// Funciones para manejar filtros y su persistencia
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable si existe
    if (typeof $.fn.DataTable !== 'undefined' && $('#prospectosTable').length > 0) {
        $('#prospectosTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/es-ES.json'
            },
            order: [[7, 'desc']], // Ordenar por fecha de registro descendente
            pageLength: 25,
            responsive: true,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    }

    // Manejar cambios en los filtros
    const periodoSelect = document.querySelector('select[name="periodo"]');
    if (periodoSelect) {
        periodoSelect.addEventListener('change', function() {
            const personalizado = this.value === 'personalizado';
            document.getElementById('fecha_inicio_container').style.display = personalizado ? 'block' : 'none';
            document.getElementById('fecha_fin_container').style.display = personalizado ? 'block' : 'none';
            
            if (!personalizado) {
                // Enviar formulario automáticamente si no es personalizado
                document.getElementById('filterForm').submit();
            }
        });
    }
    
    // Funciones de utilidad para el dashboard
    window.dashboardUtils = {
        // Formatear números con separadores de miles
        formatNumber: function(number) {
            return new Intl.NumberFormat('es-ES').format(number);
        },
        
        // Formatear fechas al formato español
        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES');
        },
        
        // Calcular porcentaje con un decimal
        calculatePercentage: function(part, total) {
            if (total === 0) return 0;
            return Math.round((part / total) * 1000) / 10; // Un decimal
        }
    };
    
    // Prevenir problemas de caché
    function addCacheBuster(url) {
        const separator = url.includes('?') ? '&' : '?';
        return url + separator + '_cb=' + new Date().getTime();
    }
    
    // Reemplazar links con versión anti-caché
    document.querySelectorAll('a').forEach(function(link) {
        if (link.href && link.href.startsWith(window.location.origin)) {
            link.addEventListener('click', function(e) {
                this.href = addCacheBuster(this.href);
            });
        }
    });
});

// Prevenir múltiples envíos de formulario
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            // Deshabilitar el botón de envío para prevenir múltiples clicks
            const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
            submitButtons.forEach(function(button) {
                button.disabled = true;
                
                // Restaurar después de 3 segundos en caso de error
                setTimeout(function() {
                    button.disabled = false;
                }, 3000);
            });
        });
    });
});